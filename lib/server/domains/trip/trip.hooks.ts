import { Trip } from "@/lib/domains/trip/trip.types"
import { TripServerService } from "./trip.service"

/**
 * Server-side Trip Hooks using Firebase Admin SDK
 * These hooks are designed for server-side operations and require userId parameter
 * since they cannot access client-side auth stores
 */
export class TripServerHooks {
  /**
   * Get a trip by ID
   * @param tripId Trip ID
   * @returns The trip data or null if not found
   */
  static async useTrip(tripId: string): Promise<Trip | null> {
    try {
      return await TripServerService.getTrip(tripId)
    } catch (error) {
      console.error("Error in useTrip (server):", error)
      return null
    }
  }

  /**
   * Get multiple trips by IDs
   * @param tripIds Array of trip IDs
   * @returns Array of trips
   */
  static async useTripsFromIds(tripIds: string[]): Promise<Trip[]> {
    try {
      return await TripServerService.getTripsFromIds(tripIds)
    } catch (error) {
      console.error("Error in useTripsFromIds (server):", error)
      return []
    }
  }

  /**
   * Get trips for a squad
   * @param squadId Squad ID
   * @returns Array of trips
   */
  static async useSquadTrips(squadId: string): Promise<Trip[]> {
    try {
      return await TripServerService.getSquadTrips(squadId)
    } catch (error) {
      console.error("Error in useSquadTrips (server):", error)
      return []
    }
  }

  /**
   * Get trips created by a user
   * @param userId User ID
   * @returns Array of trips
   */
  static async useUserCreatedTrips(userId: string): Promise<Trip[]> {
    try {
      return await TripServerService.getUserCreatedTrips(userId)
    } catch (error) {
      console.error("Error in useUserCreatedTrips (server):", error)
      return []
    }
  }

  /**
   * Get trips where user is attending (status = "going")
   * @param userId User ID
   * @returns Array of trips
   */
  static async useUserAttendingTrips(userId: string): Promise<Trip[]> {
    try {
      return await TripServerService.getUserAttendingTrips(userId)
    } catch (error) {
      console.error("Error in useUserAttendingTrips (server):", error)
      return []
    }
  }

  /**
   * Get upcoming trips for a user (attending trips that haven't ended)
   * @param userId User ID
   * @returns Array of upcoming trips
   */
  static async useUserUpcomingTrips(userId: string): Promise<Trip[]> {
    try {
      return await TripServerService.getUserUpcomingTrips(userId)
    } catch (error) {
      console.error("Error in useUserUpcomingTrips (server):", error)
      return []
    }
  }

  /**
   * Get past trips for a user (attending trips that have ended or are completed)
   * @param userId User ID
   * @returns Array of past trips
   */
  static async useUserPastTrips(userId: string): Promise<Trip[]> {
    try {
      return await TripServerService.getUserPastTrips(userId)
    } catch (error) {
      console.error("Error in useUserPastTrips (server):", error)
      return []
    }
  }

  /**
   * Get all trips for a user (both upcoming and past)
   * @param userId User ID
   * @returns Object with upcoming and past trips
   */
  static async useUserAllTrips(userId: string): Promise<{
    trips: Trip[]
    upcomingTrips: Trip[]
    pastTrips: Trip[]
  }> {
    try {
      const [upcomingTrips, pastTrips] = await Promise.all([
        TripServerService.getUserUpcomingTrips(userId),
        TripServerService.getUserPastTrips(userId),
      ])

      const allTrips = [...upcomingTrips, ...pastTrips]

      return {
        trips: allTrips,
        upcomingTrips,
        pastTrips,
      }
    } catch (error) {
      console.error("Error in useUserAllTrips (server):", error)
      return {
        trips: [],
        upcomingTrips: [],
        pastTrips: [],
      }
    }
  }

  /**
   * Get trips for multiple squads (useful when user belongs to multiple squads)
   * @param squadIds Array of squad IDs
   * @returns Array of trips from all squads
   */
  static async useMultipleSquadTrips(squadIds: string[]): Promise<Trip[]> {
    try {
      if (squadIds.length === 0) {
        return []
      }

      const tripPromises = squadIds.map((squadId) => TripServerService.getSquadTrips(squadId))
      const squadTripsArrays = await Promise.all(tripPromises)

      // Flatten and deduplicate trips (in case a trip belongs to multiple squads somehow)
      const allTrips = squadTripsArrays.flat()
      const uniqueTrips = Array.from(new Map(allTrips.map((trip) => [trip.id, trip])).values())

      return uniqueTrips
    } catch (error) {
      console.error("Error in useMultipleSquadTrips (server):", error)
      return []
    }
  }

  /**
   * Get user's trip statistics
   * @param userId User ID
   * @returns Trip statistics
   */
  static async useUserTripStats(userId: string): Promise<{
    totalTrips: number
    upcomingTrips: number
    pastTrips: number
    completedTrips: number
    createdTrips: number
  }> {
    try {
      const [attendingTrips, createdTrips] = await Promise.all([
        TripServerService.getUserAttendingTrips(userId),
        TripServerService.getUserCreatedTrips(userId),
      ])

      const now = new Date()
      let upcomingCount = 0
      let pastCount = 0
      let completedCount = 0

      attendingTrips.forEach((trip) => {
        if (trip.status === "completed") {
          completedCount++
          pastCount++
        } else if (trip.endDate) {
          const endDate = trip.endDate instanceof Date ? trip.endDate : trip.endDate.toDate()
          if (endDate < now) {
            pastCount++
          } else {
            upcomingCount++
          }
        }
      })

      return {
        totalTrips: attendingTrips.length,
        upcomingTrips: upcomingCount,
        pastTrips: pastCount,
        completedTrips: completedCount,
        createdTrips: createdTrips.length,
      }
    } catch (error) {
      console.error("Error in useUserTripStats (server):", error)
      return {
        totalTrips: 0,
        upcomingTrips: 0,
        pastTrips: 0,
        completedTrips: 0,
        createdTrips: 0,
      }
    }
  }

  /**
   * Check if user is attending a specific trip
   * @param userId User ID
   * @param tripId Trip ID
   * @returns True if user is attending the trip
   */
  static async useIsUserAttendingTrip(userId: string, tripId: string): Promise<boolean> {
    try {
      const trip = await TripServerService.getTrip(tripId)
      return trip ? trip.attendees.includes(userId) : false
    } catch (error) {
      console.error("Error in useIsUserAttendingTrip (server):", error)
      return false
    }
  }

  /**
   * Get trip attendees count
   * @param tripId Trip ID
   * @returns Number of attendees
   */
  static async useTripAttendeesCount(tripId: string): Promise<number> {
    try {
      const attendees = await TripServerService.getTripAttendees(tripId)
      return attendees.length
    } catch (error) {
      console.error("Error in useTripAttendeesCount (server):", error)
      return 0
    }
  }

  /**
   * Get trips by status for a user
   * @param userId User ID
   * @param status Trip status to filter by
   * @returns Array of trips with the specified status
   */
  static async useUserTripsByStatus(userId: string, status: string): Promise<Trip[]> {
    try {
      const attendingTrips = await TripServerService.getUserAttendingTrips(userId)
      return attendingTrips.filter((trip) => trip.status === status)
    } catch (error) {
      console.error("Error in useUserTripsByStatus (server):", error)
      return []
    }
  }

  /**
   * Get all upcoming trips across all squads/users
   * @returns Array of all upcoming trips in the system
   */
  static async useAllUpcomingTrips(): Promise<Trip[]> {
    try {
      return await TripServerService.getAllUpcomingTrips()
    } catch (error) {
      console.error("Error in useAllUpcomingTrips (server):", error)
      return []
    }
  }
}
